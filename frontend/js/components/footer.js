// Global Footer Component
class FooterComponent {
  constructor() {
    this.currentYear = new Date().getFullYear();

    (function () {
      const canvas = document.getElementById("quantum-bg");
      const ctx = canvas.getContext("2d");
      let width, height;

      function resize() {
        width = canvas.width = window.innerWidth;
        height = canvas.height = window.innerHeight;
      }

      window.addEventListener("resize", resize);
      resize();

      const particles = [];
      const count = 250;
      for (let i = 0; i < count; i++) {
        particles.push({
          x: Math.random() * width,
          y: Math.random() * height,
          r: Math.random() * 1.2 + 0.3,
          speed: Math.random() * 0.3 + 0.05,
          angle: Math.random() * Math.PI * 2,
        });
      }

      function fractal(x, y, scale) {
        return Math.sin(x * scale + Math.cos(y * scale));
      }

      function draw(t) {
        ctx.fillStyle = "rgba(0, 0, 0, 0.06)";
        ctx.fillRect(0, 0, width, height);

        for (let p of particles) {
          const n = fractal(p.x * 0.001, p.y * 0.001, 0.002);
          p.angle += n * 0.01;
          p.x += Math.cos(p.angle) * p.speed;
          p.y += Math.sin(p.angle) * p.speed;

          if (p.x < 0) p.x = width;
          if (p.x > width) p.x = 0;
          if (p.y < 0) p.y = height;
          if (p.y > height) p.y = 0;

          ctx.beginPath();
          ctx.arc(p.x, p.y, p.r, 0, Math.PI * 2);
          ctx.fillStyle = `hsl(${(p.x + p.y + t * 0.05) % 360}, 80%, 60%)`;
          ctx.fill();
        }

        requestAnimationFrame(draw);
      }


      requestAnimationFrame(draw);
    })();

  }

  getFooterHTML() {
    return `
      <footer>
        <div class="container">
          <div class="footer-content">
            <div class="footer-section">
              <p>&copy; ${this.currentYear} mermantic. All rights reserved.</p>                <div class="version-info">
                <a href="/changelog.html" class="version-badge" title="View Changelog">v0.18.2 beta</a>
              </div>
            </div>
            <div class="footer-section">
              <nav class="footer-nav">
                <h4>Quick Links</h4>
                <ul>
                  <li><a href="/">Home</a></li>
                  <li><a href="/dashboard.html">Dashboard</a></li>
                  <li><a href="/support.html" class="support-link">Support Us ❤️</a></li>
                  <li>
                    <a href="https://mermaid.js.org/intro/" target="_blank" rel="noopener" class="external-link" aria-label="Mermaid Docs (opens in new tab)">
                      Mermaid Docs
                      <svg class="external-icon" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
                        <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                        <polyline points="15,3 21,3 21,9"></polyline>
                        <line x1="10" y1="14" x2="21" y2="3"></line>
                      </svg>
                    </a>
                  </li>
                  <li><a href="/privacy.html">Privacy Policy</a></li>
                </ul>
              </nav>
            </div>
            <div class="footer-section">
              <div class="footer-info">
                <span>Create and share beautiful diagrams</span>
                <div class="footer-contact">
                  <a href="mailto:<EMAIL>"><EMAIL></a>
                </div>
              </div>
            </div>
          </div>
          <div class="footer-ai-disclosure">
            <p><em>*Information italicized with a ✨ notation denotes text that was created wholly or partially by Artificial Intelligence.</em></p>
          </div>
        </div>
      </footer>
    `;
  }

  render(containerId = 'footer-container') {
    const container = document.getElementById(containerId);
    if (!container) {
      console.error('Footer container not found:', containerId);
      return;
    }

    container.innerHTML = this.getFooterHTML();
  }

  // Static method for simple footer
  static getSimpleFooter() {
    const currentYear = new Date().getFullYear();
    return `
      <footer>
        <div class="container">
          <p>&copy; ${currentYear} mermantic. All rights reserved.</p>
        </div>
      </footer>
    `;
  }
}

// Export for use in other scripts
window.FooterComponent = FooterComponent;
