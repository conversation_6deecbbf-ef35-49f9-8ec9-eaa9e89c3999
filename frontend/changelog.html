<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Changelog - Mermantic</title>
    <link rel="stylesheet" href="nui/nui.css">
    <link rel="stylesheet" href="/css/styles.css">
    <link rel="stylesheet" href="/css/changelog.css">
</head>
<body>
    <div id="navigation-placeholder"></div>

    <main class="container py-4">
        <h1>Changelog</h1>
        
        <div class="changelog-container">
        <div class="version-entry">
                <h2>Version 0.18.2 <span class="date">(July 13, 2025)</span></h2>
                <ul>
                    <li>Fixed diagram thumbnails becoming blank after canceling editor</li>
                    <li>Fixed gallery rendering issues and improved Mermaid initialization</li>
                </ul>
            </div>
        <div class="version-entry">
                <h2>Version 0.18.1 <span class="date">(July 12, 2025)</span></h2>
                <ul>
                    <li>Added user-friendly empty state preview with syntax examples</li>
                    <li>Fixed diagram thumbnails becoming blank after canceling editor</li>
                </ul>
            </div>
            <div class="version-entry">
                <h2>Version 0.18.0 <span class="date">(July 11, 2025)</span></h2>
                <ul>
                    <li>Major improvements to diagram zoom functionality with smarter fit-to-screen calculations</li>
                    <li>Enhanced drag functionality in fullscreen mode for better diagram navigation</li>
                    <li>Improved error handling for Mermaid diagram initialization</li>
                    <li>Implemented dark theme as default for better visual consistency</li>
                    <li>Performance optimizations for chart rendering and re-rendering</li>
                    <li>Added keyboard shortcuts for common actions (Ctrl+S, Ctrl+Enter, F11, Esc)</li>
                </ul>
            </div>
            <div class="version-entry">
                <h2>Version 0.17.11 <span class="date">(July 10, 2025)</span></h2>
                <ul>
                    <li>Enhanced fullscreen edit mode with automatic diagram fit-to-screen at 100% initial zoom</li>
                </ul>
            </div>
            <div class="version-entry">
                <h2>Version 0.17.10 <span class="date">(July 8, 2025)</span></h2>
                <ul>
                    <li>Fixed incorrect filenames when exporting unsaved new diagrams</li>
                </ul>
            </div>
            <div class="version-entry">
                <h2>Version 0.17.9 <span class="date">(July 7, 2025)</span></h2>
                <ul>
                    <li>Fixed theme changing functionality in new and existing diagrams</li>
                    <li>Enhanced fullscreen edit mode with automatic diagram fit-to-screen at 100% initial zoom</li>
                </ul>
            </div>
            <div class="version-entry">
                <h2>Version 0.17.8 <span class="date">(July 6, 2025)</span></h2>
                <ul>
                    <li>Fixed dark theme not being consistently set as default for new diagrams</li>
                </ul>
            </div>
            <div class="version-entry">
                <h2>Version 0.17.7 <span class="date">(July 5, 2025)</span></h2>
                <ul>
                    <li>Added intellisense/autocomplete support to the Monaco Editor</li>
                    <li>Included smart suggestions for diagram types, directions, shapes, and connections</li>
                    <li>Added snippet-style templates for common diagram patterns</li>
                </ul>
            </div>
            <div class="version-entry">
                <h2>Version 0.17.6 <span class="date">(July 4, 2025)</span></h2>
                <ul>
                    <li>Fixed issue where valid diagrams could be incorrectly marked as invalid on load</li>
                    <li>Improved Mermaid diagram validation and preview synchronization</li>
                    <li>Enhanced error handling for diagram rendering process</li>
                </ul>
            </div>
            <div class="version-entry">
                <h2>Version 0.17.5 <span class="date">(July 3, 2025)</span></h2>
                <ul>
                    <li>Reorganized test files into a dedicated tests directory</li>
                    <li>Moved demo files to appropriate frontend directory</li>
                    <li>Updated npm test scripts to use new file locations</li>
                </ul>
            </div>
            <div class="version-entry">
                <h2>Version 0.17.4 <span class="date">(July 2, 2025)</span></h2>
                <ul>
                    <li>Cleaned up file structure</li>
                </ul>
            </div>
            <div class="version-entry">
                <h2>Version 0.17.3 <span class="date">(July 1, 2025)</span></h2>
                <ul>
                    <li>Fixed properties button visibility toggle behavior in edit mode</li>
                </ul>
            </div>
            <div class="version-entry">
                <h2>Version 0.17.2 <span class="date">(June 30, 2025)</span></h2>
                <ul>
                    <li>Fixed rendering issue with the default edit view</li>
                </ul>
            </div>
            <div class="version-entry">
                <h2>Version 0.17.1 <span class="date">(June 30, 2025)</span></h2>
                <ul>
                    <li>Added responsive hamburger navigation menu for mobile devices</li>
                    <li>Improved mobile navigation accessibility with ARIA support</li>
                    <li>Added smooth animations for menu transitions</li>
                </ul>
            </div>
            <div class="version-entry">
                <h2>Version 0.17.0 <span class="date">(June 29, 2025)</span></h2>
                <ul>
                    <li>Added Monaco Editor integration with custom Mermaid language support</li>
                    <li>Implemented syntax highlighting for all Mermaid diagram types</li>
                    <li>Custom dark theme matching site aesthetics</li>
                    <li>Graceful fallback to textarea if Monaco fails to load</li>
                </ul>
            </div>
            <div class="version-entry">
                <h2>Version 0.16.6 <span class="date">(June 28, 2025)</span></h2>
                <ul>
                    <li>Added support for transparent backgrounds in PNG exports</li>
                </ul>
            </div>
            <div class="version-entry">
                <h2>Version 0.16.5 <span class="date">(June 24, 2025)</span></h2>
                <ul>
                    <li>Fixed full height for IDE editor</li>
                </ul>
            </div>
            <div class="version-entry">
                <h2>Version 0.16.4 <span class="date">(June 23, 2025)</span></h2>
                <ul>
                    <li>Various UI enhancements</li>
                </ul>
            </div>
            <div class="version-entry">
                <h2>Version 0.16.3 <span class="date">(June 21, 2025)</span></h2>
                <ul>
                    <li>Add drag functionality to dashboard diagram view</li>
                </ul>
            </div>
            <div class="version-entry">
                <h2>Version 0.16.2 <span class="date">(June 20, 2025)</span></h2>
                <ul>
                    <li>Footer formatting fix for mobile</li>
                </ul>
            </div>
            <div class="version-entry">
                <h2>Version 0.16.1 <span class="date">(June 19, 2025)</span></h2>
                <ul>
                    <li>Fixed fullscreen view in edit mode</li>
                </ul>
            </div>
            <div class="version-entry">
                <h2>Version 0.16.0 <span class="date">(June 19, 2025)</span></h2>
                <ul>
                    <li>Added changelog</li>
                </ul>
            </div>
            <div class="version-entry">
                <h2>Version 0.15.0 <span class="date">(May 1, 2025)</span></h2>
                <ul>
                    <li>Initial release</li>
                    <li>Core diagramming functionality</li>
                    <li>User authentication system</li>
                    <li>Chart saving and sharing features</li>
                    <li>Gallery implementation</li>
                    <li>Google OAuth integration</li>
                </ul>
            </div>
        </div>
    </main>

    <div id="footer-placeholder"></div>

    <script src="nui/nui.js"></script>
    <script src="js/components/component-loader.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
