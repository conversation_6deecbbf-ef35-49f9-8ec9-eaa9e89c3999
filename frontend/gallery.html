<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Gallery - mermantic</title>
  
  <!-- Favicon -->
  <link rel="icon" type="image/x-icon" href="/favicon.ico">
  <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
  <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
  <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">

  <!-- Stylesheets -->
  <link rel="stylesheet" href="/css/styles.css">
  <link rel="stylesheet" href="/css/gallery.css">
  <link rel="stylesheet" href="/nui/nui.css">
</head>
<body>
  <!-- Global Navigation Container -->
  <div id="navigation-container"></div>

  <main class="container">
    <section class="gallery-header">
      <h2>Mermaid Chart Gallery</h2>
      <p>Explore different types of Mermaid diagrams for inspiration and reference. Click on any example to copy its syntax or use it as a template.</p>
    </section>

    <!-- Search and Filter Controls -->
    <section class="gallery-controls">
      <div class="search-container">
        <input type="text" id="gallery-search" placeholder="Search chart types..." class="search-input">
        <button id="clear-search" class="nui-button secondary">Clear</button>
      </div>
      
      <div class="filter-container">
        <label for="category-filter">Filter by category:</label>
        <select id="category-filter" class="category-select">
          <option value="all">All Categories</option>
          <option value="flowchart">Flowcharts</option>
          <option value="sequence">Sequence Diagrams</option>
          <option value="class">Class Diagrams</option>
          <option value="state">State Diagrams</option>
          <option value="gantt">Gantt Charts</option>
          <option value="pie">Pie Charts</option>
          <option value="journey">User Journey</option>
          <option value="git">Git Graphs</option>
          <option value="er">Entity Relationship</option>
          <option value="mindmap">Mind Maps</option>
          <option value="timeline">Timeline</option>
        </select>
      </div>
    </section>

    <!-- Gallery Grid -->
    <section class="gallery-grid" id="gallery-grid">
      <!-- Chart examples will be loaded here dynamically -->
    </section>

    <!-- Loading State -->
    <div id="gallery-loading" class="loading-state" style="display: none;">
      <div class="loading-spinner"></div>
      <p>Loading chart examples...</p>
    </div>

    <!-- No Results State -->
    <div id="no-results" class="no-results" style="display: none;">
      <p>No charts found matching your search criteria.</p>
      <button id="reset-filters" class="nui-button primary">Reset Filters</button>
    </div>
  </main>

  <!-- Chart Preview Modal -->
  <div id="chart-modal" class="modal-overlay" style="display: none;">
    <div class="modal-content">
      <div class="modal-header">
        <h3 id="modal-title">Chart Preview</h3>
        <button id="close-modal" class="close-button">&times;</button>
      </div>
      
      <div class="modal-body">
        <div class="chart-preview-container">
          <div class="zoom-controls">
            <button class="zoom-btn" id="zoom-out" title="Zoom Out">−</button>
            <button class="zoom-btn" id="zoom-reset" title="Reset Zoom">⌂</button>
            <button class="zoom-btn" id="zoom-in" title="Zoom In">+</button>
            <button class="zoom-btn" id="fullscreen-toggle" title="Toggle Fullscreen">⛶</button>
          </div>
          <div id="modal-chart-preview" class="chart-preview chart-zoomable"></div>
        </div>
        
        <div class="chart-code-container">
          <div class="code-header">
            <h4>Mermaid Syntax</h4>
            <div class="code-actions">
              <button id="copy-code" class="nui-button secondary">📋 Copy Code</button>
              <button id="use-template" class="nui-button primary">🎨 Use as Template</button>
            </div>
          </div>
          <pre id="modal-chart-code" class="chart-code"></pre>
        </div>
      </div>
    </div>
  </div>

  <!-- Global Footer Container -->
  <div id="footer-container"></div>

  <!-- Component Scripts -->
  <script src="/js/components/navigation.js"></script>
  <script src="/js/components/footer.js"></script>
  <script src="/js/components/analytics-manager.js"></script>
  <script src="/js/components/seo-manager.js"></script>
  <script src="/js/components/component-loader.js"></script>

  <!-- Gallery Scripts -->
  <script src="/js/mermaid.min.js"></script>
  <script src="/nui/nui.js"></script>
  <script src="/js/auth.js"></script>
  <script src="/js/gallery.js"></script>
  <script>
    // Initialize component loader
    document.addEventListener('DOMContentLoaded', () => {
      const componentLoader = new ComponentLoader();
      componentLoader.init();
      
      // Initialize gallery
      console.log('Gallery HTML: DOM loaded, checking for GalleryManager...');
      if (typeof GalleryManager !== 'undefined') {
        console.log('Gallery HTML: GalleryManager found, initializing...');
        const gallery = new GalleryManager();
        gallery.init();
      } else {
        console.error('Gallery HTML: GalleryManager not found!');
      }
    });
  </script>
</body>
</html>
