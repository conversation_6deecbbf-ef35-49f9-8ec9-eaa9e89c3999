/* Main styles for mermantic - Dark Underwater Theme */

/* Base styles */
:root {
  /* Deep Ocean Color Palette */
  --primary-color: #00d4aa;
  --primary-dark: #00a085;
  --primary-light: #4ecdc4;
  --secondary-color: #26a69a;
  --success-color: #00ffaa;
  --danger-color: #ff6b6b;
  --warning-color: #ffd93d;

  /* Background Colors */
  --background-color: #0a0f1c;
  --background-secondary: #1a2332;
  --background-tertiary: #2d3748;
  --surface-color: rgba(26, 35, 50, 0.8);
  --surface-elevated: rgba(45, 55, 72, 0.9);

  /* Text Colors */
  --text-color: #e2e8f0;
  --text-secondary: #a0aec0;
  --text-muted: #718096;
  --text-glow: #00d4aa;

  /* Border and Effects */
  --border-color: rgba(0, 212, 170, 0.2);
  --border-glow: rgba(0, 212, 170, 0.4);
  --border-radius: 8px;
  --border-radius-large: 16px;
  --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  --box-shadow-glow: 0 0 20px rgba(0, 212, 170, 0.2);
  --box-shadow-intense: 0 0 30px rgba(0, 212, 170, 0.4);

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, #0a0f1c 0%, #1a2332 50%, #2d1b69 100%);
  --gradient-surface: linear-gradient(135deg, rgba(26, 35, 50, 0.9) 0%, rgba(45, 55, 72, 0.8) 100%);
  --gradient-glow: linear-gradient(135deg, rgba(0, 212, 170, 0.1) 0%, rgba(78, 205, 196, 0.1) 100%);

  /* Animation Variables */
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;

  /* Layout measurements */
  --header-height: 64px;  /* Adjust if your header height is different */
  --footer-height: 60px;  /* Adjust if your footer height is different */
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: var(--text-color);
  background-image:
    radial-gradient(2px 2px at 20px 30px, rgba(0, 212, 170, 0.1), transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(78, 205, 196, 0.08), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(0, 212, 170, 0.12), transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(78, 205, 196, 0.1), transparent),
    radial-gradient(2px 2px at 160px 30px, rgba(0, 212, 170, 0.08), transparent),
    var(--gradient-primary);
  background-repeat: repeat, no-repeat;
  background-size: 200px 100px, cover;
  background-attachment: fixed;
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
  animation: floatParticles 30s ease-in-out infinite;

  /* Flexbox layout for sticky footer */
  display: flex;
  flex-direction: column;
}

/* Underwater atmosphere effects */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, rgba(0, 212, 170, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(78, 205, 196, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(45, 27, 105, 0.1) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
  animation: underwaterGlow 8s ease-in-out infinite alternate;
}

/* Subtle tentacle patterns integrated into body::before */

@keyframes underwaterGlow {
  0%, 100% { opacity: 0.6; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.05); }
}

@keyframes tentacleFloat {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  33% { transform: translateY(-10px) rotate(1deg); }
  66% { transform: translateY(5px) rotate(-0.5deg); }
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
  position: relative;
  z-index: 1;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  margin-bottom: 1rem;
  font-weight: 600;
  line-height: 1.2;
  color: var(--text-color);
  text-shadow: 0 0 10px rgba(0, 212, 170, 0.3);
}

h1 {
  color: var(--text-glow);
  text-shadow: 0 0 20px rgba(0, 212, 170, 0.5);
  font-weight: 700;
}

h2 {
  color: var(--primary-light);
}

p {
  margin-bottom: 1rem;
  color: var(--text-secondary);
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition-fast);
  text-shadow: 0 0 5px rgba(0, 212, 170, 0.2);
}

a:hover {
  color: var(--primary-light);
  text-shadow: 0 0 10px rgba(0, 212, 170, 0.4);
  text-decoration: none;
}

/* Header and Navigation Base Styles */
header {
  position: relative;
  z-index: 1000;
  background-color: var(--background-color);
}

header .container {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Navigation styles */
nav ul {
  display: flex;
  gap: 1.5rem;
  list-style: none;
  margin: 0;
  padding: 0;
}

nav ul li a {
  color: var(--text-color);
  text-decoration: none;
  position: relative;
  padding: 0.5rem 0;
  transition: color var(--transition-normal), border-color var(--transition-normal);
}

/* Underline hover effect */
nav ul li a::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 0;
  left: 0;
  background-color: var(--primary-color);
  transition: width var(--transition-normal);
}

nav ul li a:hover {
  color: var(--primary-light);
}

nav ul li a:hover::after {
  width: 100%;
}

/* Style for active state */
nav ul li a.active {
  color: var(--primary-color);
}

nav ul li a.active::after {
  width: 100%;
  background-color: var(--primary-color);
}

/* Responsive Navigation Styles */
.nav-toggle {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  position: relative;
  z-index: 1002;
}

.hamburger {
  display: block;
  width: 24px;
  height: 2px;
  background-color: var(--text-color);
  position: relative;
  transition: background-color var(--transition-fast);
}

.hamburger::before,
.hamburger::after {
  content: '';
  position: absolute;
  width: 24px;
  height: 2px;
  background-color: var(--text-color);
  transition: transform var(--transition-fast);
}

.hamburger::before {
  top: -8px;
}

.hamburger::after {
  bottom: -8px;
}

[aria-expanded="true"] .hamburger {
  background-color: transparent;
}

[aria-expanded="true"] .hamburger::before {
  transform: rotate(45deg);
  top: 0;
}

[aria-expanded="true"] .hamburger::after {
  transform: rotate(-45deg);
  bottom: 0;
}

@media (max-width: 768px) {
  .nav-toggle {
    display: block;
  }

  .nav-menu {
    position: fixed;
    top: 0;
    right: 0;
    width: 100%;
    height: 100vh;
    background-color: var(--background-secondary);
    padding: calc(var(--header-height) + 2rem) 2rem 2rem 2rem;
    transform: translateX(100%); /* Start off-screen to the right */
    opacity: 0;
    visibility: hidden; /* Add visibility for better accessibility */
    transition: transform var(--transition-normal),
                opacity var(--transition-normal),
                visibility var(--transition-normal);
    z-index: 1001;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }

  .nav-menu ul {
    flex-direction: column;
    gap: 1.5rem;
    padding: 0;
    margin: 0;
    list-style: none;
  }

  .nav-menu-active {
    transform: translateX(0); /* Slide in from right */
    opacity: 1;
    visibility: visible;
  }

  .nav-menu a {
    display: block;
    padding: 0.5rem 0;
    color: var(--text-color);
    text-decoration: none;
    transition: color var(--transition-fast);
  }

  .nav-menu a:hover {
    color: var(--primary-color);
  }

  .nav-menu a.active {
    color: var(--primary-color);
  }

  /* Fallback for browsers that don't support backdrop-filter */
  @supports not (backdrop-filter: blur(10px)) {
    .nav-menu {
      background-color: var(--background-secondary);
      opacity: 0.98;
    }
  }
}

/* Desktop styles */
@media (min-width: 769px) {
  .nav-menu {
    display: block;
    visibility: visible;
    opacity: 1;
    transform: none;
  }
  
  .nav-menu ul {
    display: flex;
    gap: 2rem;
    list-style: none;
    padding: 0;
    margin: 0;
  }
  
  .nav-menu a {
    color: var(--text-color);
    text-decoration: none;
    transition: color var(--transition-fast);
  }
  
  .nav-menu a:hover {
    color: var(--primary-color);
  }
  
  .nav-menu a.active {
    color: var(--primary-color);
  }
}

/* Header */
header {
  background: var(--gradient-surface);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--box-shadow-glow);
  padding: 1rem 0;
  position: relative;
  z-index: 100;
  margin-bottom: 30px;
}

header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(0, 212, 170, 0.1) 50%,
    transparent 100%);
  animation: headerGlow 4s ease-in-out infinite alternate;
}

header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}

header h1 {
  margin: 0;
  font-size: 1.8rem;
  color: var(--text-glow);
  text-shadow: 0 0 20px rgba(0, 212, 170, 0.6);
  font-weight: 700;
  letter-spacing: 1px;
}

header .logo {
  max-width: 35px; 
  text-shadow: 0 0 20px rgba(0, 212, 170, 0.6);
}

header nav ul {
  display: flex;
  list-style: none;
  gap: 1.5rem;
}

header nav ul li a {
  color: var(--text-color);
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius);
  transition: var(--transition-normal);
  position: relative;
  overflow: hidden;
}

/* Underline hover effect */
header nav ul li a::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 0;
  left: 0;
  background-color: var(--primary-color);
  transition: width var(--transition-normal);
}

header nav ul li a:hover {
  color: var(--primary-light);
}

header nav ul li a:hover::after {
  width: 100%;
}

/* Style for active state */
header nav ul li a.active {
  color: var(--primary-color);
}

header nav ul li a.active::after {
  width: 100%;
  background-color: var(--primary-color);
}

@keyframes headerGlow {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.7; }
}

/* Main content */
main {
  padding: 2rem 0;
  position: relative;
  z-index: 1;

  /* Flex grow to push footer to bottom */
  flex: 1;
}

/* FAQ Styles */
.faq-header {
  text-align: center;
  margin-bottom: 3rem;
  padding: 2rem 0;
}

.faq-header h2 {
  color: var(--text-glow);
  margin-bottom: 1rem;
  font-size: 2.5rem;
  text-shadow: 0 0 10px rgba(0, 212, 170, 0.3);
}

.faq-header p {
  color: var(--text-secondary);
  font-size: 1.1rem;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.faq-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
  margin-bottom: 3rem;
}

.faq-category {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.faq-category:hover {
  border-color: var(--border-glow);
  box-shadow: var(--box-shadow-glow);
  transform: translateY(-2px);
}

.faq-category h3 {
  color: var(--primary-color);
  margin-bottom: 1.5rem;
  font-size: 1.4rem;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 0.5rem;
}

.faq-item {
  margin-bottom: 1.5rem;
}

.faq-item:last-child {
  margin-bottom: 0;
}

.faq-item h4 {
  color: var(--text-color);
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
  font-weight: 600;
}

.faq-item p {
  color: var(--text-secondary);
  line-height: 1.6;
  font-size: 0.95rem;
}

.faq-footer {
  text-align: center;
  margin: 3rem 0;
  padding: 2rem;
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
}

.support-info h3 {
  color: var(--text-glow);
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.support-info p {
  color: var(--text-secondary);
  font-size: 1.1rem;
}

@media (max-width: 768px) {
  .faq-grid {
    grid-template-columns: 1fr;
  }

  .faq-category {
    padding: 1rem;
  }

  .faq-header h2 {
    font-size: 2rem;
  }
}

/* Footer */
footer {.footer-content {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 3rem;
  margin-bottom: 2rem;
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 3rem;
  margin-bottom: 2rem;
}

@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
  }
  
  .footer-info {
    text-align: center;
  }
}
  background: linear-gradient(to bottom, var(--surface-color), rgba(0, 212, 170, 0.05));
  backdrop-filter: blur(10px);
  border-top: 1px solid var(--border-color);
  color: var(--text-secondary);
  padding: 2rem 0;
  position: relative;
  z-index: 0;
  margin-top: auto;
  flex-shrink: 0;
}

footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg,
    transparent 0%,
    var(--primary-color) 50%,
    transparent 100%);
  box-shadow: 0 0 10px rgba(0, 212, 170, 0.5);
}

/* Footer Content Styles */
.footer-content {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  margin-bottom: 1.5rem;
}

.footer-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.footer-nav {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.footer-nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-wrap: wrap;
  gap: 1rem 2rem; /* row-gap column-gap */
}

.footer-nav li {
  margin: 0;
  min-width: 150px; /* Minimum width for each link item */
  flex: 1 1 auto; /* Allow items to grow and shrink */
}

.footer-nav a {
  color: var(--text-secondary);
  text-decoration: none;
  transition: all var(--transition-normal);
  position: relative;
  padding-left: 1.2rem;
  display: inline-flex;
  align-items: center;
  white-space: nowrap; /* Prevent text wrapping */
  width: 100%;
}

.footer-nav .external-link {
  display: inline-flex;
  align-items: center;
  white-space: nowrap;
}

.footer-nav .external-icon {
  margin-left: 4px;
  flex-shrink: 0; /* Prevent icon from shrinking */
  width: 12px;
  height: 12px;
}

@media (max-width: 768px) {
  .footer-nav ul {
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
  }

  .footer-nav li {
    min-width: unset;
    width: 100%;
    text-align: center;
  }

  .footer-nav a {
    justify-content: center;
    padding-left: 0;
  }
}

/* Hero section */
.hero {
  text-align: center;
  padding: 4rem 0;
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(0, 212, 170, 0.05) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  animation: heroGlow 6s ease-in-out infinite alternate;
}

.hero h2 {
  font-size: 2.8rem;
  margin-bottom: 1.5rem;
  color: var(--text-glow);
  text-shadow: 0 0 30px rgba(0, 212, 170, 0.6);
  position: relative;
  z-index: 1;
  font-weight: 700;
}

.hero p {
  font-size: 1.3rem;
  max-width: 800px;
  margin: 0 auto 3rem;
  color: var(--text-secondary);
  position: relative;
  z-index: 1;
  line-height: 1.7;
}

.cta-buttons {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  position: relative;
  z-index: 1;
}

@keyframes heroGlow {
  0%, 100% { opacity: 0.3; transform: translate(-50%, -50%) scale(1); }
  50% { opacity: 0.6; transform: translate(-50%, -50%) scale(1.1); }
}

/* Forms */
.auth-form {
  max-width: 500px;
  margin: 0 auto;
  padding: 2.5rem;
  background: var(--gradient-surface);
  backdrop-filter: blur(15px);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow-glow);
  position: relative;
  overflow: hidden;
}

.auth-form::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-glow);
  opacity: 0.5;
  z-index: -1;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-color);
  text-shadow: 0 0 5px rgba(0, 212, 170, 0.2);
}

.form-actions {
  margin-top: 2rem;
  margin-bottom: 60px;
}

.auth-redirect {
  margin-top: 1.5rem;
  text-align: center;
}

/* Social login */
.social-login {
  margin-top: 2rem;
  text-align: center;
}

.social-login p {
  margin-bottom: 1rem;
  color: var(--secondary-color);
  position: relative;
}

.social-login p::before,
.social-login p::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 30%;
  height: 1px;
  background-color: var(--border-color);
}

.social-login p::before {
  left: 0;
}

.social-login p::after {
  right: 0;
}

.google-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  background: var(--surface-elevated);
  color: var(--text-color);
  border: 1px solid var(--border-color);
  width: 100%;
  margin-bottom: 1rem;
  transition: var(--transition-normal);
  border-radius: var(--border-radius);
  padding: 0.75rem;
  backdrop-filter: blur(10px);
}

.google-btn:hover {
  background: var(--surface-color);
  border-color: var(--border-glow);
  box-shadow: var(--box-shadow-glow);
  transform: translateY(-2px);
}

.error-message {
  background: linear-gradient(135deg, var(--danger-color), #ff5252);
  color: white;
  padding: 1rem;
  border-radius: var(--border-radius);
  margin-bottom: 1.5rem;
  display: none;
  border: 1px solid rgba(255, 107, 107, 0.3);
  box-shadow: 0 0 15px rgba(255, 107, 107, 0.2);
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}

.success-message {
  background: linear-gradient(135deg, var(--success-color), #00e676);
  color: white;
  padding: 1rem;
  border-radius: var(--border-radius);
  margin-bottom: 1.5rem;
  display: none;
  border: 1px solid rgba(0, 255, 170, 0.3);
  box-shadow: 0 0 15px rgba(0, 255, 170, 0.2);
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}

.forgot-password {
  text-align: center;
  margin-top: 1rem;
}

.forgot-password a {
  color: var(--secondary-color);
  font-size: 0.9rem;
  text-decoration: none;
}

/* Dashboard */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: var(--gradient-surface);
  backdrop-filter: blur(15px);
  border-radius: var(--border-radius-large);
  border: 1px solid var(--border-color);
  box-shadow: var(--box-shadow-glow);
  position: relative;
  overflow: hidden;
}

.dashboard-title-section {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.dashboard-title-section h2 {
  margin: 0;
}

.global-theme-selector {
  display: flex;
  align-items: center;
  gap: 1rem;
  position: relative;
  background: var(--surface-elevated);
  padding: 0.75rem 1rem;
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  box-shadow: var(--box-shadow-glow);
  transition: var(--transition-normal);
}

.global-theme-selector:hover {
  border-color: var(--border-glow);
  box-shadow: var(--box-shadow-intense);
}

.global-theme-selector label {
  color: var(--text-color);
  font-weight: 500;
  white-space: nowrap;
  margin: 0;
}

.global-theme-selector select {
  background: var(--background-secondary);
  color: var(--text-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 0.5rem;
  min-width: 150px;
  font-size: 0.9rem;
  transition: var(--transition-normal);
}

.global-theme-selector select:hover {
  border-color: var(--border-glow);
}

.global-theme-selector select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px var(--border-glow);
}

.global-theme-selector select option {
  background: var(--background-secondary);
  color: var(--text-color);
  padding: 0.5rem;
}

/* Folder input container styling */
.folder-input-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.folder-input-container .nui-input {
  margin-bottom: 0;
}

.folder-suggestions {
  font-size: 0.9rem;
}

.folder-suggestions option:first-child {
  color: var(--text-muted);
  font-style: italic;
}

/* Form help text styling */
.form-help {
  display: block;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: var(--text-muted);
  font-style: italic;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.chart-card {
  background: var(--gradient-surface);
  backdrop-filter: blur(15px);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow-glow);
  padding: 1.5rem;
  transition: var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.chart-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-glow);
  opacity: 0;
  transition: var(--transition-normal);
  z-index: -1;
}

.chart-card:hover {
  transform: translateY(-5px);
  border-color: var(--border-glow);
  box-shadow: var(--box-shadow-intense);
}

.chart-card:hover::before {
  opacity: 0.3;
}

.chart-card h4 {
  margin-bottom: 1rem;
}

.chart-preview-small {
  height: 150px;
  overflow: hidden;
  margin-bottom: 1rem;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-preview-small svg {
  max-width: 100%;
  max-height: 150px;
  display: block;
  position: relative !important;
  z-index: 1;
}

.chart-actions {
  display: flex;
  gap: 0.5rem;
}

/* IDE Layout */
.chart-editor.ide-layout {
  display: flex;
  flex-direction: column;
  max-height: calc(100vh); 
  background: var(--background-color);
  overflow: hidden; /* Prevent the editor itself from scrolling */
}

.ide-container {
  display: flex;
  flex-direction: column;
  min-height: calc(100vh);
  margin: 0;
  padding: 0;
}

.ide-content {
  flex: 1;
  display: flex;
  flex-direction: row; /* Changed to row to place panels side by side */
  gap: 1rem;
  background: var(--surface-color);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  margin: 0;
  padding: 1rem;
  min-height: 0; /* Allow flex to properly calculate height */
  overflow: hidden; /* Prevent content from breaking out of container */
}

/* Main editor area */
.editor-area {
  flex: 2; /* Takes up more space than the properties panel */
  display: flex;
  flex-direction: column;
  min-width: 0; /* Prevents flex items from overflowing */
  background: var(--surface-elevated);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

/* Properties panel - styles moved to line ~1190 */

/* Ensure any embedded content (like Monaco editor) fills the space */
.editor-area > * {
  flex: 1;
  height: 100%;
  min-height: 0; /* Allows flex to properly calculate height */
}

.ide-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  background: var(--gradient-surface);
  backdrop-filter: blur(15px);
  border-bottom: 1px solid var(--border-color);
  flex-shrink: 0;
  min-height: 60px;
  margin-top: 75px; /* Position below navigation container */
}

.toolbar-left h3 {
  margin: 0;
  font-size: 1.1rem;
  color: var(--text-color);
}

.toolbar-center,
.toolbar-right {
  display: flex;
  align-items: center;
}

.toolbar-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.toolbar-btn {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.5rem 0.75rem;
  background: transparent;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  color: var(--text-color);
  cursor: pointer;
  transition: var(--transition-normal);
  font-size: 0.9rem;
}

.toolbar-btn:hover {
  background: var(--background-secondary);
  border-color: var(--primary-color);
}

.toolbar-btn .icon {
  font-size: 0.8rem;
}

.properties-panel {
  width: 300px;
  min-width: 250px;
  max-width: 400px;
  background: var(--gradient-surface);
  backdrop-filter: blur(15px);
  border-right: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  overflow-y: auto;
  transition: transform 0.3s ease, opacity 0.3s ease, width 0.3s ease, min-width 0.3s ease;
  transform: translateX(0);
  opacity: 1;
}

.properties-panel.hidden {
  transform: translateX(-100%);
  opacity: 0;
  width: 0;
  min-width: 0;
  border-right: none;
  overflow: hidden;
}

.properties-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--background-secondary);
}

.properties-header h4 {
  margin: 0;
  font-size: 1rem;
  color: var(--text-color);
}

.close-btn {
  background: none;
  border: none;
  color: var(--text-color);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: var(--border-radius);
  transition: var(--transition-normal);
}

.close-btn:hover {
  background: var(--background-color);
  color: var(--primary-color);
}

.properties-content {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
}

.properties-content .form-group {
  margin-bottom: 1rem;
}

.properties-content .form-group:last-child {
  margin-bottom: 0;
}

/* Editor */
.editor-container {
  display: flex;
  flex: 1;
  min-height: 0;
  gap: 1px;
  background: var(--border-color);
}

.editor-panel, .preview-panel {
  background: var(--gradient-surface);
  backdrop-filter: blur(15px);
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
  max-height: calc(100vh - 300px); /* Ensure panels don't overflow into footer */
  overflow: hidden; /* Prevent content from breaking out */
}

.editor-header, .preview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--background-secondary);
  flex-shrink: 0;
}

.editor-header h4, .preview-header h4 {
  margin: 0;
  font-size: 1rem;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.editor-panel {
  border-right: 1px solid var(--border-color);
}

.mermaid-editor {
  flex: 1;
  min-height: 0;
  font-family: 'Fira Code', 'Consolas', monospace;
  padding: 1rem;
  border: none;
  border-radius: 0;
  resize: none;
  background: var(--background-secondary);
  color: var(--text-color);
  transition: var(--transition-normal);
  outline: none;
  overflow: auto; /* Ensure content stays within bounds */
}

.mermaid-preview {
  flex: 1;
  min-height: 300px; /* Ensure minimum height for Mermaid rendering */
  min-width: 200px;  /* Ensure minimum width for Mermaid rendering */
  overflow: auto;
  padding: 1rem;
  background: var(--background-secondary);
  position: relative;
}

/* Ensure mermaid elements have proper dimensions */
.mermaid-preview .mermaid {
  width: 100%;
  min-height: 200px;
}

.mermaid-preview .mermaid svg {
  max-width: 100%;
  height: auto;
}

.help-panel {
  background: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1rem;
  margin-top: 1rem;
  max-height: 200px;
  overflow-y: auto;
}

.help-panel h5 {
  margin: 0 0 0.75rem 0;
  color: var(--text-color);
  font-size: 0.9rem;
}

.help-examples {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.help-example {
  padding: 0.25rem 0.5rem;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: 0.8rem;
  transition: var(--transition-normal);
}

.help-example:hover {
  background: var(--primary-hover);
}

.help-shortcuts, .help-unicode {
  font-size: 0.8rem;
  color: var(--text-muted);
  margin-bottom: 0.75rem;
}

.help-shortcuts:last-child, .help-unicode:last-child {
  margin-bottom: 0;
}

/* Preview zoom controls styling */
.preview-zoom-controls {
  display: flex;
  gap: 0.25rem;
  align-items: center;
}

.zoom-btn {
  background: var(--background-secondary);
  border: 1px solid var(--border-color);
  color: var(--text-color);
  width: 28px;
  height: 28px;
  border-radius: var(--border-radius);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  font-weight: bold;
  transition: var(--transition-normal);
}

.zoom-btn:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.zoom-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: var(--background-secondary);
  color: var(--text-muted);
}

.zoom-btn:disabled:hover {
  background: var(--background-secondary);
  color: var(--text-muted);
  border-color: var(--border-color);
}

.preview-controls {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.preview-download-group {
  display: flex;
  gap: 0.25rem;
}

/* Modal zoom controls (gallery-style) */
.chart-preview-container {
  background: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 2rem;
  margin-bottom: 1.5rem;
  text-align: center;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: auto;
  height: 100%;
}

.modal-zoom-controls {
  position: absolute;
  top: 1rem;
  right: 1rem;
  display: flex;
  gap: 0.5rem;
  z-index: 10;
  background: rgba(0, 0, 0, 0.8);
  padding: 0.5rem;
  border-radius: var(--border-radius);
  backdrop-filter: blur(5px);
}

.modal-zoom-controls .zoom-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.modal-zoom-controls .zoom-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

.modal-zoom-controls .zoom-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: rgba(255, 255, 255, 0.05);
}

.modal-zoom-controls .zoom-btn:disabled:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.2);
}

.view-chart-container {
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 100%;
  min-height: 400px;
  cursor: grab;
  user-select: none;
  -webkit-user-select: none;
}

.view-chart-container:active {
  cursor: grabbing;
}

.view-chart-container .mermaid {
  position: relative;
  display: inline-block;
  min-width: 100%;
  min-height: 100%;
}

/* Ensure the container takes full height in fullscreen mode */
.modal.fullscreen .view-chart-container {
  height: calc(100vh - 120px); /* Adjust for header/footer */
  min-height: unset;
}

.mermaid-editor:focus {
  outline: none;
  border-color: var(--border-glow);
  box-shadow: var(--box-shadow-glow);
}

.mermaid-preview {
  width: 100%;
  min-height: 300px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1rem;
  background: var(--background-secondary);
  overflow: auto;
  position: relative;
}

.mermaid-preview::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-glow);
  opacity: 0.1;
  pointer-events: none;
  border-radius: var(--border-radius);
}

.mermaid-preview .error-message {
  color: var(--danger-color);
  padding: 1rem;
  border: 1px solid var(--danger-color);
  border-radius: var(--border-radius);
  background-color: rgba(220, 53, 69, 0.1);
  font-family: system-ui, -apple-system, sans-serif;
  white-space: pre-wrap;
  margin-top: 0.5rem;
  line-height: 1.5;
  text-align: left;
}

.mermaid-preview .error-message strong {
  font-weight: 600;
  display: block;
  margin-bottom: 0.5rem;
}

.chart-preview-small .error-message {
  font-size: 0.8rem;
  padding: 0.5rem;
  overflow: auto;
  max-height: 140px;
}

/* Syntax indicator */
.syntax-indicator {
  font-size: 0.8rem;
  padding: 2px 6px;
  border-radius: 3px;
  font-weight: normal;
  margin-left: 8px;
}

.syntax-indicator.valid {
  background-color: var(--success-color);
  color: var(--background-color);
}

.syntax-indicator.invalid {
  background-color: var(--danger-color);
  color: white;
}

.syntax-indicator.checking {
  background-color: var(--secondary-color);
  color: white;
}

.syntax-indicator.warning {
  background-color: #ffc107;
  color: #212529;
}

/* Help panel */
.help-toggle {
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 12px;
  cursor: pointer;
  margin-left: 8px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.help-toggle:hover {
  background: var(--primary-dark-color);
}

.help-panel {
  background: var(--light-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1rem;
  margin-top: 0.5rem;
  font-size: 0.9rem;
}

.help-panel h5 {
  margin: 0 0 0.5rem 0;
  color: var(--primary-color);
}

.help-examples {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.help-example {
  background: white;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 0.25rem 0.5rem;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s;
}

.help-example:hover {
  background: var(--primary-color);
  color: white;
}

.help-shortcuts {
  font-size: 0.8rem;
  color: var(--secondary-color);
}

.help-unicode {
  margin-top: 10px;
  font-size: 0.8rem;
  color: var(--secondary-color);
  padding: 8px;
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 4px;
}

.help-shortcuts kbd {
  background: var(--dark-color);
  color: white;
  padding: 2px 4px;
  border-radius: 2px;
  font-size: 0.7rem;
}

/* Hide footer when in fullscreen edit mode */
body.fullscreen-edit #footer-container {
  display: none !important;
}

/* Fullscreen IDE editor */
.chart-editor.ide-layout.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100%;
  background: var(--background-color);
  z-index: 9999;
  padding: 10px;
  box-sizing: border-box;
}

.chart-editor.fullscreen .form-header {
  flex-shrink: 0;
  margin-bottom: 1rem;
}

.chart-editor.fullscreen .editor-container {
  flex: 1;
  display: flex;
  gap: 10px;
  min-height: 0;
}

.chart-editor.fullscreen .editor-panel {
  flex: 1; /* Take up 1/2 of the space */
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.chart-editor.fullscreen .editor-panel,
.chart-editor.fullscreen .preview-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.chart-editor.fullscreen .mermaid-editor {
  flex: 1;
  min-height: 0;
  resize: none;
}

.chart-editor.fullscreen .mermaid-preview {
  flex: 1;
  overflow: auto;
  padding: 10px;
  background: var(--background-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  cursor: grab;
}

.chart-editor.fullscreen .mermaid-preview .mermaid {
  width: max-content; /* Allow content to determine width */
  min-width: 100%;
  min-height: max-content; /* Allow content to determine height */
  display: block; /* Remove flex to allow proper scrolling */
  text-align: center; /* Center the SVG horizontally */
}

.chart-editor.fullscreen .mermaid-preview .mermaid svg {
  max-width: none; /* Allow SVG to be larger than container */
  width: auto;
  height: auto;
  display: block;
  margin: 0 auto; /* Center horizontally */
}

.chart-editor.fullscreen .form-actions {
  flex-shrink: 0;
  margin-top: 1rem;
}

/* Responsive fullscreen editor for mobile */
@media (max-width: 768px) {
  .chart-editor.fullscreen .editor-container {
    flex-direction: column;
    gap: 0.5rem;
  }

  .chart-editor.fullscreen .editor-panel {
    flex: 1; /* Editor takes more space on mobile */
  }

  .chart-editor.fullscreen .preview-panel {
    flex: 1; /* Preview takes equal space on mobile */
    min-height: 200px; /* Ensure minimum height for preview */
  }
}

/* Monaco Editor Integration */
.monaco-mermaid-editor {
  height: 100%;
  width: 100%;
  border: none;
  outline: none;
}

.monaco-editor {
  border-radius: 0;
}

.monaco-editor .margin {
  background-color: var(--background-secondary) !important;
}

.monaco-editor .monaco-editor-background {
  background-color: var(--background-secondary) !important;
}

/* Copy button styling */
.copy-button {
  padding: 5px 10px;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s ease;
}

.copy-button:hover {
  background: var(--primary-hover);
}

/* Fullscreen modal */
.modal.fullscreen {
  background-color: var(--background-color);
}

.modal.fullscreen .modal-content {
  width: 100vw;
  height: 100vh;
  max-width: none;
  margin: 0;
  border-radius: 0;
  display: flex;
  flex-direction: column;
}

.modal.fullscreen .modal-body {
  flex: 1;
  max-height: none;
  padding: 2rem;
  overflow: auto; /* Enable scrolling for fullscreen modal */
}

.modal.fullscreen .view-chart-container {
  height: auto; /* Allow container to expand with content */
  min-height: 100%; /* Ensure minimum height fills the modal */
  border: none;
  padding: 0;
  background: transparent;
  overflow: visible; /* Ensure content can extend beyond container */
}

.modal.fullscreen .modal-header {
  flex-shrink: 0;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid var(--border-color);
  margin-top: 75px; /* Position below navigation container */
}

.modal.fullscreen .modal-footer {
  flex-shrink: 0;
  padding: 1.5rem 2rem;
  border-top: 1px solid var(--border-color);
}

/* Modal zoom controls in fullscreen */
.modal.fullscreen .modal-zoom-controls {
  position: fixed;
  top: 100px; /* Positioned below navigation bar */
  right: 20px;
  z-index: 10000;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(15px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  opacity: 0.85;
}

.modal.fullscreen .modal-zoom-controls:hover {
  opacity: 1;
}



/* Folder input container */
.folder-input-container {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.folder-input-container input {
  flex: 1;
}

.folder-suggestions {
  min-width: 200px;
  max-width: 250px;
}

/* Chart cards with folder info */
.chart-card .chart-folder {
  font-size: 0.8rem;
  color: var(--secondary-color);
  margin-bottom: 0.25rem;
  font-weight: 500;
}

.chart-card .chart-folder:empty {
  display: none;
}

.chart-card .chart-notes {
  font-size: 0.8rem;
  color: var(--text-color);
  margin-top: 0.5rem;
  line-height: 1.4;
  max-height: 3.5rem;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2; /* Added standard property for compatibility */
  -webkit-box-orient: vertical;
}

.chart-card .chart-notes:empty {
  display: none;
}

/* Folder sections */
.folder-section {
  margin-bottom: 2rem;
}

.folder-header {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  padding: 0.5rem 0;
  border-bottom: 2px solid var(--primary-color);
}

.folder-icon {
  margin-right: 0.5rem;
  color: var(--primary-color);
  font-size: 1.2rem;
}

.folder-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--primary-color);
  margin: 0;
}

.folder-count {
  margin-left: auto;
  background: var(--primary-color);
  color: white;
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Demo section */
.demo-section {
  margin: 3rem 0;
}

.demo-section h3 {
  margin-bottom: 2rem;
}

.demo-section .editor-panel h4,
.demo-section .preview-panel h4 {
  margin: 0;
  padding: 1rem;
  font-size: 1rem;
  color: var(--text-color);
  background: var(--background-secondary);
  border-bottom: 1px solid var(--border-color);
}

.features {
  margin: 3rem 0;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
}

.feature-card {
  background: var(--gradient-surface);
  backdrop-filter: blur(15px);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow-glow);
  padding: 2rem;
  transition: var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-glow);
  opacity: 0;
  transition: var(--transition-normal);
  z-index: -1;
}

.feature-card:hover {
  transform: translateY(-5px);
  border-color: var(--border-glow);
  box-shadow: var(--box-shadow-intense);
}

.feature-card:hover::before {
  opacity: 0.2;
}

.feature-card h4 {
  color: var(--primary-light);
  margin-bottom: 1rem;
}

/* Shared chart */
.shared-chart {
  max-width: 800px;
  margin: 0 auto;
}

/* Modal */
.modal {
  display: none;
  position: fixed;
  z-index: 999999;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
  isolation: isolate;
}

/* Ensure modal appears above everything when open */
body.modal-open {
  overflow: hidden;
}

body.modal-open .modal {
  z-index: 2147483647; /* Maximum z-index value */
}

.modal-content {
  background: var(--gradient-surface);
  backdrop-filter: blur(20px);
  margin: 5% auto;
  padding: 0;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-large);
  width: 80%;
  max-width: 900px;
  box-shadow: var(--box-shadow-intense);
  position: relative;
  overflow: hidden;
  z-index: 1000000;
}

.modal-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-glow);
  opacity: 0.1;
  z-index: -1;
}

.modal-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(0, 212, 170, 0.05);
}

.modal-header h3 {
  margin: 0;
  color: var(--text-glow);
  text-shadow: 0 0 10px rgba(0, 212, 170, 0.4);
}

.modal-body {
  padding: 1.5rem;
  max-height: 70vh;
  overflow-y: auto;
}

.modal-footer {
  padding: 1.5rem;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  background: rgba(0, 212, 170, 0.05);
}

.close-modal {
  color: var(--text-secondary);
  font-size: 1.5rem;
  font-weight: bold;
  cursor: pointer;
  transition: var(--transition-fast);
}

.close-modal:hover {
  color: var(--primary-light);
  text-shadow: 0 0 10px rgba(0, 212, 170, 0.4);
}

.view-chart-container {
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 100%;
  min-height: 400px;
  cursor: grab;
  user-select: none;
  -webkit-user-select: none;
}

.view-chart-container:active {
  cursor: grabbing;
}

.view-chart-container .mermaid {
  position: relative;
  display: inline-block;
  min-width: 100%;
  min-height: 100%;
}

/* Support page styles */
.support-header {
  text-align: center;
  margin: 2rem 0 3rem;
  padding: 2rem;
  background: var(--gradient-surface);
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow);
}

.support-header h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: var(--primary-color);
  text-shadow: 0 0 15px var(--border-glow);
}

.support-content {
  max-width: 900px;
  margin: 0 auto;
  padding: 2rem;
}

.support-message {
  text-align: center;
  margin-bottom: 3rem;
}

.support-message p {
  font-size: 1.1rem;
  line-height: 1.8;
  margin-bottom: 1.5rem;
}

.support-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin: 3rem 0;
}

.support-card {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--box-shadow);
  transition: var(--transition-normal);
}

.support-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--box-shadow-glow);
  border-color: var(--border-glow);
}

.support-card h3 {
  color: var(--primary-light);
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
}

.support-card ul {
  list-style: none;
  padding: 0;
}

.support-card ul li {
  color: var(--text-secondary);
  margin-bottom: 1rem;
  padding-left: 1.5rem;
  position: relative;
}

.support-card ul li::before {
  content: "✦";
  color: var(--primary-color);
  position: absolute;
  left: 0;
  opacity: 0.8;
}

.support-action {
  text-align: center;
  background: var(--surface-elevated);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--box-shadow);
}

.support-action h3 {
  color: var(--primary-light);
  margin-bottom: 1rem;
}

.support-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 1.5rem;
  padding: 0.8rem 2rem;
  font-size: 1.1rem;
  border-radius: var(--border-radius);
  background: var(--primary-color);
  color: var(--background-color);
  border: none;
  cursor: pointer;
  transition: var(--transition-normal);
  text-decoration: none;
}

.support-button:hover {
  background: var(--primary-light);
  transform: translateY(-2px);
  box-shadow: var(--box-shadow-glow);
}

.support-button .external-icon {
  transition: var(--transition-fast);
}

.support-button:hover .external-icon {
  transform: translate(2px, -2px);
}

.thank-you-message {
  margin-top: 4rem;
  padding: 2rem;
  background: var(--gradient-surface);
  border-radius: var(--border-radius);
  text-align: center;
}

.thank-you-message h3 {
  color: var(--primary-light);
  margin-bottom: 1rem;
}

.thank-you-message p {
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

.video-container {
  text-align: center;
}

#quantum-bg {
  position: fixed;
  top: 0; left: 0;
  width: 100vw;
  height: 100vh;
  z-index: -1;
  opacity: 0.12;
  pointer-events: none;
  filter: blur(0.5px) brightness(1.2);
}
