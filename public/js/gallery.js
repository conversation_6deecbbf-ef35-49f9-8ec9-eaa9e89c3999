// Gallery Manager for Mermaid Chart Examples
class GalleryManager {
  constructor() {
    this.charts = [];
    this.filteredCharts = [];
    this.currentFilter = 'all';
    this.currentSearch = '';
    this.mermaidInitialized = false;
    this.currentZoom = 1;
    this.isFullscreen = false;
    this.isDragging = false;
    this.dragStart = { x: 0, y: 0 };
    this.chartPosition = { x: 0, y: 0 };
  }

  // Initialize the gallery
  async init() {
    console.log('Gallery: Starting initialization...');
    this.initializeMermaid();
    this.loadChartExamples();
    this.setupEventListeners();

    // Add a small delay to ensure DOM is fully ready
    setTimeout(() => {
      this.renderGallery();
    }, 100);

    console.log('Gallery: Initialization complete');
  }

  // Initialize Mermaid with proper configuration
  initializeMermaid() {
    if (typeof mermaid !== 'undefined') {
      // Always reinitialize to ensure gallery has proper configuration
      // This prevents conflicts with dashboard Mermaid settings
      mermaid.initialize({
        startOnLoad: false,
        securityLevel: 'loose',
        theme: 'dark',
        logLevel: 'error',
        fontFamily: 'monospace',
        flowchart: {
          useMaxWidth: true,
          htmlLabels: true,
          curve: 'linear'
        },
        sequence: {
          useMaxWidth: true,
          wrap: true,
          showSequenceNumbers: false
        },
        gantt: {
          useMaxWidth: true
        },
        journey: {
          useMaxWidth: true
        },
        er: {
          useMaxWidth: true
        },
        suppressErrors: true
      });
      this.mermaidInitialized = true;
      console.log('Gallery: Mermaid initialized with gallery-specific configuration');
    }
  }

  // Load chart examples data
  loadChartExamples() {
    this.charts = [
      // Flowcharts
      {
        id: 'flowchart-basic',
        title: 'Basic Flowchart',
        category: 'flowchart',
        description: 'A simple flowchart showing decision-making process',
        code: `graph TD
    A[Start] --> B{Decision}
    B -->|Yes| C[Action 1]
    B -->|No| D[Action 2]
    C --> E[End]
    D --> E`
      },
      {
        id: 'flowchart-complex',
        title: 'Complex Flowchart',
        category: 'flowchart',
        description: 'Advanced flowchart with subgraphs and styling',
        code: `graph TB
    subgraph "User Interface"
        A[Login Page] --> B{Valid Credentials?}
    end
    
    subgraph "Authentication"
        B -->|Yes| C[Generate Token]
        B -->|No| D[Show Error]
        C --> E[Redirect to Dashboard]
        D --> A
    end
    
    subgraph "Dashboard"
        E --> F[Load User Data]
        F --> G[Display Charts]
    end`
      },

      // Sequence Diagrams
      {
        id: 'sequence-basic',
        title: 'Basic Sequence Diagram',
        category: 'sequence',
        description: 'Simple interaction between two participants',
        code: `sequenceDiagram
    participant A as Alice
    participant B as Bob
    A->>B: Hello Bob!
    B-->>A: Hello Alice!
    A->>B: How are you?
    B-->>A: I'm good, thanks!`
      },
      {
        id: 'sequence-api',
        title: 'API Interaction',
        category: 'sequence',
        description: 'API request/response flow with error handling',
        code: `sequenceDiagram
    participant C as Client
    participant S as Server
    participant DB as Database
    
    C->>+S: POST /api/login
    S->>+DB: Validate credentials
    DB-->>-S: User data
    
    alt successful login
        S-->>C: 200 OK + JWT token
    else invalid credentials
        S-->>C: 401 Unauthorized
    end`
      },

      // Class Diagrams
      {
        id: 'class-basic',
        title: 'Basic Class Diagram',
        category: 'class',
        description: 'Simple class relationships',
        code: `classDiagram
    class Animal {
        +String name
        +int age
        +makeSound()
        +move()
    }
    
    class Dog {
        +String breed
        +bark()
        +wagTail()
    }
    
    class Cat {
        +String color
        +meow()
        +purr()
    }
    
    Animal <|-- Dog
    Animal <|-- Cat`
      },

      // State Diagrams
      {
        id: 'state-basic',
        title: 'State Machine',
        category: 'state',
        description: 'Simple state machine for user authentication',
        code: `stateDiagram-v2
    [*] --> Idle
    Idle --> Authenticating : login
    Authenticating --> Authenticated : success
    Authenticating --> Idle : failure
    Authenticated --> Idle : logout
    Authenticated --> [*]`
      },

      // Gantt Charts
      {
        id: 'gantt-project',
        title: 'Project Timeline',
        category: 'gantt',
        description: 'Project management timeline with milestones',
        code: `gantt
    title Project Development Timeline
    dateFormat YYYY-MM-DD
    section Planning
    Research & Analysis    :done, research, 2024-01-01, 2024-01-14
    Requirements Gathering :done, requirements, 2024-01-08, 2024-01-21
    Design Phase          :active, design, 2024-01-15, 2024-02-05
    section Development
    Backend Development   :backend, after design, 30d
    Frontend Development  :frontend, after design, 25d
    Integration          :integration, after backend, 10d
    section Testing
    Unit Testing         :testing, after integration, 15d
    User Acceptance      :uat, after testing, 10d`
      },

      // Pie Charts
      {
        id: 'pie-languages',
        title: 'Programming Languages',
        category: 'pie',
        description: 'Distribution of programming language usage',
        code: `pie title Programming Language Usage
    "JavaScript" : 35
    "Python" : 25
    "Java" : 20
    "TypeScript" : 12
    "Other" : 8`
      },

      // User Journey
      {
        id: 'journey-shopping',
        title: 'User Shopping Journey',
        category: 'journey',
        description: 'Customer experience journey through online shopping',
        code: `journey
    title User Shopping Experience
    section Discovery
      Visit Website: 5: User
      Browse Products: 4: User
      Read Reviews: 3: User
    section Purchase
      Add to Cart: 5: User
      Checkout: 3: User
      Payment: 2: User
    section Post-Purchase
      Receive Product: 5: User
      Leave Review: 4: User`
      },

      // Git Workflow (using flowchart)
      {
        id: 'git-workflow',
        title: 'Git Workflow',
        category: 'git',
        description: 'Git branching and merging workflow visualization',
        code: `graph TD
    A[Initial Commit] --> B[Main Branch]
    B --> C[Create Feature Branch]
    C --> D[Develop Feature]
    D --> E[Commit Changes]
    E --> F[Create Pull Request]
    F --> G{Code Review}
    G -->|Approved| H[Merge to Main]
    G -->|Changes Needed| D
    H --> I[Deploy to Production]
    I --> J[Tag Release]

    style A fill:#e1f5fe
    style H fill:#c8e6c9
    style I fill:#fff3e0`
      },

      // Entity Relationship
      {
        id: 'er-ecommerce',
        title: 'E-commerce Database',
        category: 'er',
        description: 'Entity relationship diagram for e-commerce system',
        code: `erDiagram
    CUSTOMER {
        int customer_id PK
        string first_name
        string last_name
        string email
        string phone
    }
    
    ORDER {
        int order_id PK
        int customer_id FK
        date order_date
        decimal total_amount
        string status
    }
    
    PRODUCT {
        int product_id PK
        string name
        string description
        decimal price
        int stock_quantity
    }
    
    ORDER_ITEM {
        int order_id FK
        int product_id FK
        int quantity
        decimal unit_price
    }
    
    CUSTOMER ||--o{ ORDER : places
    ORDER ||--o{ ORDER_ITEM : contains
    PRODUCT ||--o{ ORDER_ITEM : "ordered in"`
      },

      // Mind Map (using flowchart as mindmap syntax may not be supported)
      {
        id: 'mindmap-project',
        title: 'Project Planning',
        category: 'mindmap',
        description: 'Mind map for project planning and organization',
        code: `graph TD
    A[Project Planning] --> B[Requirements]
    A --> C[Design]
    A --> D[Development]
    A --> E[Testing]
    A --> F[Deployment]

    B --> B1[Functional]
    B --> B2[Non-functional]
    B --> B3[User Stories]

    C --> C1[UI/UX]
    C --> C2[Architecture]
    C --> C3[Database Schema]

    D --> D1[Frontend]
    D --> D2[Backend]
    D --> D3[API Design]

    E --> E1[Unit Tests]
    E --> E2[Integration Tests]
    E --> E3[User Acceptance]

    F --> F1[CI/CD Pipeline]
    F --> F2[Production Environment]
    F --> F3[Monitoring]`
      },

      // Timeline (using flowchart)
      {
        id: 'timeline-company',
        title: 'Company Milestones',
        category: 'timeline',
        description: 'Timeline showing company growth milestones',
        code: `graph LR
    A[2020<br/>Company Founded<br/>10 Employees] --> B[2021<br/>Series A Funding<br/>50 Employees]
    B --> C[2022<br/>Product v2.0<br/>100 Employees]
    C --> D[2023<br/>IPO<br/>500 Employees]
    D --> E[2024<br/>Market Leader<br/>Global Expansion]

    style A fill:#e3f2fd
    style B fill:#e8f5e8
    style C fill:#fff3e0
    style D fill:#fce4ec
    style E fill:#f3e5f5`
      }
    ];

    this.filteredCharts = [...this.charts];
    console.log(`Gallery: Loaded ${this.charts.length} chart examples`);
  }

  // Setup event listeners
  setupEventListeners() {
    // Search functionality
    const searchInput = document.getElementById('gallery-search');
    const clearSearch = document.getElementById('clear-search');

    if (searchInput) {
      searchInput.addEventListener('input', (e) => {
        this.currentSearch = e.target.value.toLowerCase();
        this.filterCharts();
      });
    }

    if (clearSearch) {
      clearSearch.addEventListener('click', () => {
        searchInput.value = '';
        this.currentSearch = '';
        this.filterCharts();
      });
    }

    // Category filter
    const categoryFilter = document.getElementById('category-filter');
    if (categoryFilter) {
      categoryFilter.addEventListener('change', (e) => {
        this.currentFilter = e.target.value;
        this.filterCharts();
      });
    }

    // Reset filters
    const resetFilters = document.getElementById('reset-filters');
    if (resetFilters) {
      resetFilters.addEventListener('click', () => {
        searchInput.value = '';
        categoryFilter.value = 'all';
        this.currentSearch = '';
        this.currentFilter = 'all';
        this.filterCharts();
      });
    }

    // Modal functionality
    this.setupModalListeners();
  }

  // Setup modal event listeners
  setupModalListeners() {
    const modal = document.getElementById('chart-modal');
    const closeModal = document.getElementById('close-modal');

    if (closeModal) {
      closeModal.addEventListener('click', () => {
        this.closeModal();
      });
    }

    if (modal) {
      modal.addEventListener('click', (e) => {
        if (e.target === modal) {
          this.closeModal();
        }
      });
    }

    // Copy code functionality
    const copyCode = document.getElementById('copy-code');
    if (copyCode) {
      copyCode.addEventListener('click', () => {
        this.copyCodeToClipboard();
      });
    }

    // Use as template functionality
    const useTemplate = document.getElementById('use-template');
    if (useTemplate) {
      useTemplate.addEventListener('click', () => {
        this.useAsTemplate();
      });
    }

    // Zoom controls
    const zoomIn = document.getElementById('zoom-in');
    const zoomOut = document.getElementById('zoom-out');
    const zoomReset = document.getElementById('zoom-reset');
    const fullscreenToggle = document.getElementById('fullscreen-toggle');

    if (zoomIn) {
      zoomIn.addEventListener('click', () => this.zoomIn());
    }
    if (zoomOut) {
      zoomOut.addEventListener('click', () => this.zoomOut());
    }
    if (zoomReset) {
      zoomReset.addEventListener('click', () => this.resetZoom());
    }
    if (fullscreenToggle) {
      fullscreenToggle.addEventListener('click', () => this.toggleFullscreen());
    }

    // Escape key to exit fullscreen
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.isFullscreen) {
        this.toggleFullscreen();
      }
    });
  }

  // Filter charts based on search and category
  filterCharts() {
    this.filteredCharts = this.charts.filter(chart => {
      const matchesSearch = chart.title.toLowerCase().includes(this.currentSearch) ||
        chart.description.toLowerCase().includes(this.currentSearch) ||
        chart.category.toLowerCase().includes(this.currentSearch);

      const matchesCategory = this.currentFilter === 'all' || chart.category === this.currentFilter;

      return matchesSearch && matchesCategory;
    });

    this.renderGallery();
  }

  // Render the gallery grid
  renderGallery() {
    const galleryGrid = document.getElementById('gallery-grid');
    const noResults = document.getElementById('no-results');

    console.log(`Gallery: Rendering gallery with ${this.filteredCharts.length} charts`);

    if (!galleryGrid) {
      console.error('Gallery: gallery-grid element not found!');
      return;
    }

    if (this.filteredCharts.length === 0) {
      galleryGrid.style.display = 'none';
      if (noResults) noResults.style.display = 'block';
      return;
    }

    galleryGrid.style.display = 'grid';
    if (noResults) noResults.style.display = 'none';

    galleryGrid.innerHTML = this.filteredCharts.map(chart => `
      <div class="chart-card" data-chart-id="${chart.id}">
        <div class="chart-card-header">
          <h3 class="chart-title">${chart.title}</h3>
          <span class="chart-category">${chart.category}</span>
        </div>
        <p class="chart-description">${chart.description}</p>
        <div class="chart-preview-mini" id="preview-${chart.id}">
          <div class="zoom-controls">
            <button class="zoom-btn mini-zoom-in" data-chart-id="${chart.id}" title="Zoom In">+</button>
            <button class="zoom-btn mini-fullscreen" data-chart-id="${chart.id}" title="View Fullscreen">⛶</button>
          </div>
          <div class="mermaid chart-zoomable" data-chart-id="${chart.id}">${chart.code}</div>
        </div>
        <div class="chart-actions">
          <button class="nui-button secondary copy-btn" data-code="${encodeURIComponent(chart.code)}">📋 Copy</button>
          <button class="nui-button primary preview-btn" data-chart-id="${chart.id}">👁️ Preview</button>
        </div>
      </div>
    `).join('');

    // Add event listeners to chart cards
    this.attachCardListeners();

    // Render mini previews with a delay to ensure DOM is ready
    setTimeout(() => {
      this.renderMiniPreviews();
    }, 200);
  }

  // Attach event listeners to chart cards
  attachCardListeners() {
    // Preview buttons
    document.querySelectorAll('.preview-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        e.stopPropagation();
        const chartId = btn.dataset.chartId;
        this.openModal(chartId);
      });
    });

    // Copy buttons
    document.querySelectorAll('.copy-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        e.stopPropagation();
        const code = decodeURIComponent(btn.dataset.code);
        this.copyToClipboard(code);
      });
    });

    // Card click to open modal
    document.querySelectorAll('.chart-card').forEach(card => {
      card.addEventListener('click', (e) => {
        // Don't open modal if clicking on zoom controls
        if (e.target.closest('.zoom-controls')) {
          return;
        }
        const chartId = card.dataset.chartId;
        this.openModal(chartId);
      });
    });

    // Mini zoom controls
    document.querySelectorAll('.mini-zoom-in').forEach(btn => {
      btn.addEventListener('click', (e) => {
        e.stopPropagation();
        const chartId = btn.dataset.chartId;
        this.zoomMiniPreview(chartId, 1.5);
      });
    });

    document.querySelectorAll('.mini-fullscreen').forEach(btn => {
      btn.addEventListener('click', (e) => {
        e.stopPropagation();
        const chartId = btn.dataset.chartId;
        this.openModal(chartId);
        // Auto-enter fullscreen after modal opens
        setTimeout(() => this.toggleFullscreen(), 100);
      });
    });
  }

  // Render mini previews in gallery cards
  async renderMiniPreviews() {
    console.log(`Gallery: Rendering ${this.filteredCharts.length} mini previews...`);

    for (const chart of this.filteredCharts) {
      const previewElement = document.querySelector(`#preview-${chart.id} .mermaid`);
      console.log(`Gallery: Processing chart ${chart.id}, element found:`, !!previewElement, 'mermaid initialized:', this.mermaidInitialized);

      if (previewElement && this.mermaidInitialized) {
        try {
          const id = `mermaid-mini-${chart.id}`;
          previewElement.id = id;

          await mermaid.run({
            nodes: [previewElement],
            suppressErrors: true
          });
          console.log(`Gallery: Successfully rendered preview for ${chart.id}`);
        } catch (error) {
          console.warn(`Gallery: Error rendering mini preview for ${chart.id}:`, error);
          previewElement.innerHTML = '<div style="color: var(--text-muted); text-align: center; padding: 2rem;">Preview unavailable</div>';
        }
      } else {
        console.warn(`Gallery: Skipping ${chart.id} - element: ${!!previewElement}, mermaid: ${this.mermaidInitialized}`);
      }
    }
  }

  // Open modal with chart details
  openModal(chartId) {
    const chart = this.charts.find(c => c.id === chartId);
    if (!chart) return;

    const modal = document.getElementById('chart-modal');
    const modalTitle = document.getElementById('modal-title');
    const modalPreview = document.getElementById('modal-chart-preview');
    const modalCode = document.getElementById('modal-chart-code');

    if (modalTitle) modalTitle.textContent = chart.title;
    if (modalCode) modalCode.textContent = chart.code;

    // Store current chart for modal actions
    this.currentModalChart = chart;

    // Reset zoom and fullscreen state
    this.currentZoom = 1;
    this.chartPosition = { x: 0, y: 0 };
    this.isFullscreen = false;

    // Render chart in modal
    if (modalPreview && this.mermaidInitialized) {
      modalPreview.innerHTML = `<div class="mermaid chart-zoomable" id="modal-mermaid">${chart.code}</div>`;

      setTimeout(async () => {
        try {
          const mermaidElement = document.getElementById('modal-mermaid');
          if (mermaidElement) {
            await mermaid.run({
              nodes: [mermaidElement],
              suppressErrors: true
            });

            // Setup drag functionality after chart is rendered
            this.setupDragFunctionality();
          }
        } catch (error) {
          console.warn('Error rendering modal chart:', error);
          modalPreview.innerHTML = '<div style="color: var(--text-muted); text-align: center; padding: 2rem;">Chart rendering failed</div>';
        }
      }, 100);
    }

    if (modal) modal.style.display = 'flex';
  }

  // Close modal
  closeModal() {
    const modal = document.getElementById('chart-modal');
    if (modal) modal.style.display = 'none';
    this.currentModalChart = null;
  }

  // Copy code to clipboard
  async copyToClipboard(code) {
    try {
      await navigator.clipboard.writeText(code);
      this.showToast('Code copied to clipboard!', 'success');
    } catch (error) {
      console.error('Failed to copy code:', error);
      this.showToast('Failed to copy code', 'error');
    }
  }

  // Copy code from modal
  copyCodeToClipboard() {
    if (this.currentModalChart) {
      this.copyToClipboard(this.currentModalChart.code);
    }
  }

  // Use chart as template (redirect to dashboard)
  useAsTemplate() {
    if (this.currentModalChart) {
      // Store the template code in localStorage
      localStorage.setItem('templateCode', this.currentModalChart.code);
      localStorage.setItem('templateTitle', this.currentModalChart.title);

      // Redirect to dashboard
      window.location.href = '/dashboard.html?template=true';
    }
  }

  // Zoom functionality
  zoomIn() {
    this.currentZoom = Math.min(this.currentZoom * 1.2, 3);
    this.applyZoom();
  }

  zoomOut() {
    this.currentZoom = Math.max(this.currentZoom / 1.2, 0.5);
    this.applyZoom();
  }

  resetZoom() {
    this.currentZoom = 1;
    this.chartPosition = { x: 0, y: 0 };
    this.applyZoom();
  }

  applyZoom() {
    const chartElement = document.getElementById('modal-mermaid');
    if (chartElement) {
      chartElement.style.transform = `scale(${this.currentZoom}) translate(${this.chartPosition.x}px, ${this.chartPosition.y}px)`;

      // Add/remove zoomed class for cursor styling
      if (this.currentZoom > 1) {
        chartElement.classList.add('zoomed');
      } else {
        chartElement.classList.remove('zoomed');
      }
    }
  }

  // Mini preview zoom
  zoomMiniPreview(chartId, scale) {
    const previewElement = document.querySelector(`#preview-${chartId} .mermaid`);
    if (previewElement) {
      const currentScale = previewElement.style.transform.match(/scale\(([\d.]+)\)/);
      const newScale = currentScale ? parseFloat(currentScale[1]) * scale : scale;
      previewElement.style.transform = `scale(${Math.min(newScale, 2)})`;
      previewElement.style.transformOrigin = 'center center';
    }
  }

  // Fullscreen functionality
  toggleFullscreen() {
    const modal = document.getElementById('chart-modal');
    const modalContent = modal?.querySelector('.modal-content');

    if (!modal || !modalContent) return;

    this.isFullscreen = !this.isFullscreen;

    if (this.isFullscreen) {
      modal.classList.add('fullscreen');
      modalContent.classList.add('fullscreen');
      this.resetZoom(); // Reset zoom when entering fullscreen
    } else {
      modal.classList.remove('fullscreen');
      modalContent.classList.remove('fullscreen');
      this.resetZoom(); // Reset zoom when exiting fullscreen
    }
  }

  // Setup drag functionality for zoomed charts
  setupDragFunctionality() {
    const chartElement = document.getElementById('modal-mermaid');
    if (!chartElement) return;

    chartElement.addEventListener('mousedown', (e) => {
      if (this.currentZoom > 1) {
        this.isDragging = true;
        this.dragStart = { x: e.clientX - this.chartPosition.x, y: e.clientY - this.chartPosition.y };
        chartElement.style.cursor = 'grabbing';
      }
    });

    document.addEventListener('mousemove', (e) => {
      if (this.isDragging && this.currentZoom > 1) {
        this.chartPosition = {
          x: e.clientX - this.dragStart.x,
          y: e.clientY - this.dragStart.y
        };
        this.applyZoom();
      }
    });

    document.addEventListener('mouseup', () => {
      if (this.isDragging) {
        this.isDragging = false;
        const chartElement = document.getElementById('modal-mermaid');
        if (chartElement) {
          chartElement.style.cursor = this.currentZoom > 1 ? 'move' : 'grab';
        }
      }
    });
  }

  // Show toast notification
  showToast(message, type = 'info') {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    toast.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: ${type === 'success' ? 'var(--success-color)' : type === 'error' ? 'var(--danger-color)' : 'var(--primary-color)'};
      color: white;
      padding: 1rem 1.5rem;
      border-radius: var(--border-radius);
      box-shadow: var(--box-shadow);
      z-index: 10000;
      animation: slideIn 0.3s ease;
    `;

    document.body.appendChild(toast);

    // Remove toast after 3 seconds
    setTimeout(() => {
      toast.style.animation = 'slideOut 0.3s ease';
      setTimeout(() => {
        if (toast.parentNode) {
          toast.parentNode.removeChild(toast);
        }
      }, 300);
    }, 3000);
  }
}

// Export for global use
window.GalleryManager = GalleryManager;
